# Global Protest Tracker - MongoDB Database Setup

This document provides comprehensive instructions for setting up the MongoDB database for the Global Protest Tracker application.

## 🏗️ Database Architecture

The application uses MongoDB with the following collections:

### Core Collections

1. **protests** - Main protest data
   - Geographic indexing for location-based queries
   - Text search indexing for keyword searches
   - Status and category indexing for filtering

2. **users** - User accounts and authentication
   - Unique indexes on email and username
   - Password hashing with bcrypt
   - User type system (regular, verified_activist, admin)

3. **alerts** - User alert subscriptions
   - Keyword-based matching
   - Location and category filtering
   - Frequency settings (immediate, daily, weekly)

4. **user_content** - User-generated content
   - Reports, photos, videos, updates
   - Moderation workflow (pending, approved, rejected)
   - Like and report tracking

5. **admin_actions** - Audit trail
   - Complete log of administrative actions
   - Target tracking and metadata storage

### Supporting Collections

- **user_types** - User permission definitions
- **content_likes** - User likes on content
- **content_reports** - Content moderation reports

## 🚀 Quick Setup

### Prerequisites

1. **Python 3.8+** installed
2. **MongoDB** installed and running
3. **Virtual environment** (recommended)

### Installation Steps

1. **Clone and navigate to the project:**
   ```bash
   cd Protest/backend
   ```

2. **Create and activate virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Run the setup script:**
   ```bash
   python scripts/setup.py
   ```

4. **Initialize the database:**
   ```bash
   python scripts/init_database.py
   ```

5. **Start the application:**
   ```bash
   python app.py
   ```

## 📋 Manual Setup

If you prefer manual setup or encounter issues with the automated scripts:

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. MongoDB Installation

#### macOS (using Homebrew)
```bash
brew tap mongodb/brew
brew install mongodb-community
brew services start mongodb-community
```

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install -y mongodb
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### Windows
Download and install from [MongoDB Community Server](https://www.mongodb.com/try/download/community)

#### Docker
```bash
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

### 3. Environment Configuration

Create a `.env` file in the backend directory:

```env
# Flask Configuration
FLASK_CONFIG=development
SECRET_KEY=your-secret-key-here

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/protest_tracker
USE_TEST_DATA=false

# API Configuration
API_RATE_LIMIT=100 per hour
CORS_ORIGINS=http://localhost:3000,http://localhost:5000
```

### 4. Database Initialization

Run the initialization script:

```bash
python scripts/init_database.py
```

This will:
- Create database indexes
- Set up user types
- Create a default admin user
- Seed sample data from test_data.json

## 🔧 Configuration Options

### MongoDB Connection Strings

- **Local MongoDB:** `mongodb://localhost:27017/protest_tracker`
- **MongoDB Atlas:** `mongodb+srv://username:<EMAIL>/protest_tracker`
- **Docker:** `mongodb://mongo:27017/protest_tracker`

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/protest_tracker` |
| `USE_TEST_DATA` | Use JSON test data instead of MongoDB | `false` |
| `SECRET_KEY` | Flask secret key | `dev-secret-key` |
| `FLASK_CONFIG` | Configuration mode | `development` |

## 🧪 Testing the Setup

### 1. Health Check
```bash
curl http://localhost:5000/health
```

### 2. API Endpoints
```bash
# Get all protests
curl http://localhost:5000/api/protests

# Search protests
curl http://localhost:5000/api/search/protests?q=climate

# Get specific protest
curl http://localhost:5000/api/protests/1
```

### 3. Database Verification

Connect to MongoDB and verify collections:

```bash
mongo protest_tracker
> show collections
> db.protests.count()
> db.users.count()
```

## 🔐 Default Admin User

The initialization script creates a default admin user:

- **Email:** `<EMAIL>`
- **Password:** `admin123`

**⚠️ IMPORTANT:** Change this password in production!

## 📊 Sample Data

The database is seeded with sample data including:

- 3 sample protests (Climate Action March, Workers Rights Rally, Education Funding Protest)
- 1 test user account
- 1 sample alert subscription
- User type definitions (regular, verified_activist, admin)

## 🛠️ Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Ensure MongoDB is running: `sudo systemctl status mongod`
   - Check connection string in `.env` file
   - Verify firewall settings

2. **Import Errors**
   - Ensure you're in the correct directory (`backend/`)
   - Activate virtual environment
   - Install dependencies: `pip install -r requirements.txt`

3. **Permission Errors**
   - Make scripts executable: `chmod +x scripts/*.py`
   - Check MongoDB data directory permissions

4. **Port Conflicts**
   - Default MongoDB port: 27017
   - Default Flask port: 5000
   - Change ports in configuration if needed

### Database Reset

To completely reset the database:

```bash
mongo protest_tracker
> db.dropDatabase()
> exit
python scripts/init_database.py
```

## 🔄 Data Migration

For production deployments or data migration:

1. **Export data:**
   ```bash
   mongodump --db protest_tracker --out backup/
   ```

2. **Import data:**
   ```bash
   mongorestore --db protest_tracker backup/protest_tracker/
   ```

## 📈 Performance Optimization

The database includes optimized indexes for:

- Geographic queries (2dsphere index on location.coordinates)
- Text search (text index on title and description)
- Date range queries (index on start_date)
- User lookups (unique indexes on email and username)
- Alert matching (indexes on keywords and user_id)

## 🔒 Security Considerations

1. **Change default passwords** in production
2. **Use environment variables** for sensitive configuration
3. **Enable MongoDB authentication** in production
4. **Use SSL/TLS** for database connections
5. **Implement rate limiting** for API endpoints
6. **Regular security updates** for dependencies

## 📚 Additional Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [PyMongo Documentation](https://pymongo.readthedocs.io/)
- [Flask Documentation](https://flask.palletsprojects.com/)
- [Project API Documentation](./blueprints/README.md)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the application logs
3. Verify MongoDB logs: `/var/log/mongodb/mongod.log`
4. Check the GitHub issues for similar problems
